'use client';

import { Check, ChevronsUpDown } from 'lucide-react';
import { But<PERSON> } from '@workspace/ui/components/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from '@workspace/ui/components/command';
import { Popover, PopoverContent, PopoverTrigger } from '@workspace/ui/components/popover';
import { cn } from '@workspace/ui/lib/utils';
import { useState, useMemo } from 'react';
import { useAgent } from '@/components/providers/agent-provider';
import { useRouter } from 'next/navigation';
import { useAgents, getAgentRoute } from '@/hooks/use-agents';

export function AgentSelector() {
  const [open, setOpen] = useState(false);
  const { currentAgent, setAgent } = useAgent();
  const router = useRouter();
  const { agents, loading, error } = useAgents();

  // 统一的显示名称获取函数
  const getDisplayName = useMemo(() => {
    return (agent: (typeof agents)[number] | null) => {
      if (!agent) return '';
      // 优先使用 originalId，如果没有则使用 id，确保一致性
      return agent.originalId || agent.id || '';
    };
  }, []);

  const handleAgentSelect = (agent: (typeof agents)[number]) => {
    try {
      const routePath = getAgentRoute(agent.id, agents);
      router.push(routePath);
      setAgent(agent);
      setOpen(false);
    } catch (error) {
      console.error('Failed to navigate to agent route:', error);
      // 可以在这里添加错误处理，比如显示 toast 通知
    }
  };

  // 渲染状态按钮的通用组件
  const renderStateButton = (text: string, isError = false) => (
    <Button
      variant='ghost'
      disabled
      className={cn(
        'h-full w-full justify-between rounded-md border-0 bg-transparent px-3 text-xs shadow-none focus:ring-0',
        isError && 'text-red-500'
      )}>
      {text}
      <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
    </Button>
  );

  // 优化loading逻辑：只有在真正没有任何agent信息时才显示loading
  // 如果已经有currentAgent（即使是fallback），就不显示loading
  if (loading && agents.length === 0 && !currentAgent) return renderStateButton('Loading...');
  if (error && agents.length === 0 && !currentAgent) return renderStateButton('Error', true);

  // 如果有currentAgent但没有agents数据，显示当前agent但禁用下拉
  if (!agents.length && currentAgent) {
    return (
      <Button
        variant='ghost'
        disabled
        className='h-full w-full justify-between rounded-md border-0 bg-transparent px-3 text-xs shadow-none focus:ring-0'>
        {getDisplayName(currentAgent)}
        <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
      </Button>
    );
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='ghost'
          role='combobox'
          aria-expanded={open}
          className='h-full w-full justify-between rounded-md border-0 bg-transparent px-3 text-xs shadow-none hover:bg-transparent focus:ring-0'>
          {getDisplayName(currentAgent)}
          <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-[200px] p-0'>
        <Command>
          <CommandInput placeholder='Search...' />
          <CommandList>
            <CommandEmpty>未找到 agent。</CommandEmpty>
            <CommandGroup>
              {agents.map((agent) => (
                <CommandItem
                  key={agent.id}
                  value={agent.id}
                  onSelect={() => handleAgentSelect(agent)}
                  className='text-xs'>
                  <Check className={cn('mr-2 h-4 w-4', currentAgent?.id === agent.id ? 'opacity-100' : 'opacity-0')} />
                  {getDisplayName(agent)}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
